// Main JavaScript for Portfolio Website Generator 

// Light/Dark Mode Toggle
const themeToggle = document.getElementById('theme-toggle');
const root = document.documentElement;

function setTheme(theme) {
  root.setAttribute('data-theme', theme);
  localStorage.setItem('theme', theme);
  // Optionally update the toggle button icon/text
  if (themeToggle) {
    themeToggle.textContent = theme === 'dark' ? '🌞' : '🌓';
  }
}

function getPreferredTheme() {
  const stored = localStorage.getItem('theme');
  if (stored) return stored;
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

document.addEventListener('DOMContentLoaded', () => {
  setTheme(getPreferredTheme());
  if (themeToggle) {
    themeToggle.addEventListener('click', () => {
      const current = root.getAttribute('data-theme');
      setTheme(current === 'dark' ? 'light' : 'dark');
    });
  }
});

// Section fade-in on scroll
const observer = new window.IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('visible');
      observer.unobserve(entry.target);
    }
  });
}, { threshold: 0.15 });

document.querySelectorAll('main section').forEach(section => {
  observer.observe(section);
});

// Project tag filtering
const filterButtons = document.querySelectorAll('.project-filter');
const projectGroups = document.querySelectorAll('.project-group');
function showProjectGroups(tag) {
  projectGroups.forEach(group => {
    if (tag === 'all' || group.dataset.tag === tag) {
      group.classList.add('active');
    } else {
      group.classList.remove('active');
    }
  });
}
filterButtons.forEach(btn => {
  btn.addEventListener('click', () => {
    filterButtons.forEach(b => b.classList.remove('active'));
    btn.classList.add('active');
    showProjectGroups(btn.dataset.tag);
  });
});
// Show all by default
showProjectGroups('all');

// Journey card expand/collapse
const journeyToggles = document.querySelectorAll('.journey-toggle');
journeyToggles.forEach(btn => {
  btn.addEventListener('click', () => {
    const details = btn.parentElement.querySelector('.journey-details');
    const expanded = btn.getAttribute('aria-expanded') === 'true';
    btn.setAttribute('aria-expanded', !expanded);
    btn.textContent = expanded ? 'Show Details' : 'Hide Details';
    if (details) {
      details.hidden = expanded;
    }
  });
});

// Timeline entry animation
const timelineEntries = document.querySelectorAll('.timeline-entry');
const timelineObserver = new window.IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('visible');
      timelineObserver.unobserve(entry.target);
    }
  });
}, { threshold: 0.15 });
timelineEntries.forEach(entry => timelineObserver.observe(entry));



// Updated recruiterProfile object with LinkedIn data
const recruiterProfile = {
  name: 'Vasilis Klonis',
  title: 'Senior Director, Service Delivery @ Persado',
  skills: [
    // AI & Automation Expertise
    { name: 'LLM Integration', level: 5 },
    { name: 'Workflow Automation (n8n)', level: 5 },
    { name: 'AI-Assisted Development', level: 5 },
    { name: 'Image Recognition', level: 4 },
    { name: 'AI Research', level: 4 },
    // Technical Skills
    { name: 'JavaScript', level: 5 },
    { name: 'Python', level: 5 },
    { name: 'HTML', level: 5 },
    { name: 'CSS', level: 5 },
    { name: 'Selenium WebDriver', level: 5 },
    { name: 'Photoshop', level: 5 },
    { name: 'Email Marketing', level: 5 },
    { name: 'A/B Testing', level: 5 },
    { name: 'Google Analytics', level: 5 },
    { name: 'Google Adwords', level: 5 },
    { name: 'SEO/SEM', level: 5 },
    { name: 'Marketing Automation', level: 5 },
    { name: 'Data Migration', level: 5 },
    { name: 'Systems Integration', level: 5 },
    { name: 'Wordpress', level: 4 },
    { name: 'Joomla', level: 4 },
    { name: 'QA Automation', level: 5 },
    { name: 'Security Best Practices', level: 4 },
    { name: 'Marketing AI Solutions', level: 5 },
    { name: 'Cross-Channel Automation', level: 5 },
    { name: 'Technical Security', level: 4 },
    { name: 'Quality Assurance', level: 5 },
    
    // Management & Process Skills
    { name: 'Team Leadership', level: 5 },
    { name: 'Process Management', level: 5 },
    { name: 'Project Management', level: 5 },
    { name: 'Agile Methodologies', level: 5 },
    { name: 'Lean Six Sigma', level: 4 },
    { name: 'kanban', level: 5 },
    { name: 'Scrum', level: 4 },
    { name: 'PRINCE', level: 4 },
    { name: 'Resource Allocation', level: 5 },
    { name: 'wrike project management', level: 5 },
    { name: 'jira, trello, clickup', level: 4 },
    
    // Business Skills
    { name: 'Budget Control', level: 5 },
    { name: 'P&L Management', level: 5 },
    { name: 'Client Engagement', level: 5 },
    { name: 'AI Strategy Leadership', level: 5 },
    { name: 'Process Optimization', level: 5 },
    { name: 'Operational Efficiency', level: 5 }
  ],
  experience: [
    {
      role: 'Senior Director, Service Delivery',
      company: 'Persado',
      years: 'September 2024 - Present',
      achievements: [
        'Lead global service delivery operations focusing on process scaling and automation',
        'Pioneered AI-powered workflow automation and support',
        'Drive operational innovation through new technologies and scalable solutions',
        'Align service delivery with organizational goals to maximize profitability',
        'Manage high-profile client engagements for multichannel campaigns'
      ]
    },
    {
      role: 'Director, Service Delivery',
      company: 'Persado',
      years: 'July 2018 - October 2024',
      achievements: [
        'Managed service delivery team executing end-to-end marketing campaigns',
        'Designed internal tools to enhance automation and scalability',
        'Monitored operations budget and service marginality',
        'Oversaw new client contract evaluations and operating models'
      ]
    },
    {
      role: 'Team Leader of Service Delivery (EMEA-LATAM)',
      company: 'Persado',
      years: 'October 2016 - June 2018',
      achievements: [
        'Managed transition from SQL to Hadoop for client ETL processes',
        'Developed onboarding materials and team training programs',
        'Implemented performance and process enhancements',
        'Managed resources planning and capacity monitoring'
      ]
    },
    {
      role: '(Web) Service Delivery Engineer',
      company: 'Persado',
      years: 'January 2014 - September 2016',
      achievements: [
        'Technical implementation of web projects and systems integration',
        'Built scripts/tools to accelerate platform integrations',
        'Developed QA tools library for web projects',
        'Created cross-browser testing solutions for email campaigns'
      ]
    },
    {
      role: 'Head of Marketing - Greece',
      company: 'Master D Hellas',
      years: 'March 2012 - December 2012',
      achievements: [
        'Reduced cost per lead by 42% across all digital campaigns',
        'Increased remarketing audience reach by 120%',
        'Decreased marketing costs by 30% YoY',
        'Developed custom web-based project management tool'
      ]
    }
  ],
  education: [
    {
      degree: 'Master of Engineering',
      institution: 'University of Thessaly',
      years: '',
      focus: 'Computer & Communication Engineering'
    },
    {
      degree: 'Bachelor of Engineering',
      institution: 'University of Thessaly',
      years: '',
      focus: 'Computer & Communication Engineering - IT, Software Engineering, Telecommunications'
    }
  ],
  projects: [
    { 
      name: 'QA Automation Framework', 
      tags: ['Python', 'Selenium', 'Testing'], 
      description: 'Developed Python/Selenium QA framework for web projects to ensure campaign quality' 
    },
    { 
      name: 'Marketing Automation Tools', 
      tags: ['JavaScript', 'Automation', 'RPA'], 
      description: 'Designed internal tools to enhance automation levels and scalability for campaign delivery' 
    },
    { 
      name: 'ETL Process Migration', 
      tags: ['Data Migration', 'Hadoop', 'ETL'], 
      description: 'Managed transition from SQL to Hadoop for client data processing pipelines' 
    },
    { 
      name: 'Adwords Optimization System', 
      tags: ['Google Adwords', 'Automation', 'Bidding'], 
      description: 'Created custom automatic bid adjustment tools to optimize ad performance' 
    }
  ],
  hidden: {
    motivation: 'Deliver flawless campaigns through precise execution and innovative process enhancements while continuously upgrading technical skills',
    relocation: 'Based in Greece, open to remote opportunities',
    languages: ['Greek (Native)', 'English (Professional)'],
    certifications: [
      'Applications of Everyday Leadership',
      'Digital Analytics for Marketing Professionals',
      'Performance Marketing Management Essentials',
      'Lean Six Sigma foundations',
      'Change Management Professional Certificate',
      'Project Management for Business Process Improvement',
      'Effective Communication & Soft skills for Managers',
      'Mind Mapping for Business Analysis and Project Management'
    ]
  }
};

// Recruiter Match section logic
const recruiterForm = document.getElementById('recruiter-match-form');
const jobAdInput = document.getElementById('job-ad-input');
const matchResult = document.getElementById('recruiter-match-result');
const analyzeBtn = document.getElementById('analyze-btn');

if (recruiterForm && jobAdInput && matchResult && analyzeBtn) {
  recruiterForm.addEventListener('submit', async (e) => {
    e.preventDefault();
    matchResult.classList.remove('active');
    matchResult.textContent = '';
    analyzeBtn.disabled = true;
    analyzeBtn.textContent = 'Analyzing...';
    matchResult.style.display = 'block';
    matchResult.textContent = 'Analyzing with AI.This may take a while...';
    try {
      const res = await fetch('http://localhost:3001/api/gemini-match', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobAd: jobAdInput.value,
          profile: recruiterProfile
        })
      });
      const data = await res.json();
      if (data.result) {
        matchResult.textContent = data.result;
        matchResult.classList.add('active');
      } else {
        matchResult.textContent = 'No response from AI.';
      }
    } catch (err) {
      matchResult.textContent = 'Error: Could not connect to AI service.';
    } finally {
      analyzeBtn.disabled = false;
      analyzeBtn.textContent = 'Analyze Match';
    }
  });
}

// Tech Stack Visualizer
class TechStackVisualizer {
  constructor() {
    this.modal = document.getElementById('tech-stack-modal');
    this.openBtn = document.getElementById('tech-stack-btn');
    this.closeBtn = document.getElementById('close-tech-stack');
    this.networkContainer = document.getElementById('tech-stack-network');
    this.resetBtn = document.getElementById('reset-view');
    this.highlightBtn = document.getElementById('highlight-connections');
    this.groupBtn = document.getElementById('group-by-category');

    this.width = 0;
    this.height = 0;
    this.svg = null;
    this.simulation = null;
    this.nodes = [];
    this.links = [];
    this.isHighlightMode = false;

    this.initEventListeners();
    this.createTechStackData();
  }

  initEventListeners() {
    if (this.openBtn) {
      this.openBtn.addEventListener('click', () => this.openModal());
    }

    if (this.closeBtn) {
      this.closeBtn.addEventListener('click', () => this.closeModal());
    }

    if (this.modal) {
      this.modal.addEventListener('click', (e) => {
        if (e.target === this.modal) this.closeModal();
      });
    }

    if (this.resetBtn) {
      this.resetBtn.addEventListener('click', () => this.resetView());
    }

    if (this.highlightBtn) {
      this.highlightBtn.addEventListener('click', () => this.toggleHighlightMode());
    }

    if (this.groupBtn) {
      this.groupBtn.addEventListener('click', () => this.groupByCategory());
    }

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.modal.style.display === 'block') {
        this.closeModal();
      }
    });
  }

  createTechStackData() {
    // Define technology nodes with categories and experience levels
    this.nodes = [
      // Programming Languages
      { id: 'javascript', name: 'JavaScript', category: 'programming', level: 5, color: '#3b82f6' },
      { id: 'python', name: 'Python', category: 'programming', level: 4, color: '#3b82f6' },
      { id: 'html', name: 'HTML/CSS', category: 'programming', level: 5, color: '#3b82f6' },
      { id: 'sql', name: 'SQL', category: 'programming', level: 4, color: '#3b82f6' },

      // Marketing Platforms
      { id: 'salesforce', name: 'Salesforce Marketing Cloud', category: 'marketing', level: 4, color: '#10b981' },
      { id: 'responsys', name: 'Responsys ESP', category: 'marketing', level: 5, color: '#10b981' },
      { id: 'adobe-campaign', name: 'Adobe Campaign', category: 'marketing', level: 3, color: '#10b981' },
      { id: 'experian', name: 'Experian CCMP', category: 'marketing', level: 5, color: '#10b981' },
      { id: 'adobe-target', name: 'Adobe Target', category: 'marketing', level: 5, color: '#10b981' },
      { id: 'optimizely', name: 'Optimizely/VWO', category: 'marketing', level: 4, color: '#10b981' },
      { id: 'google-analytics', name: 'Google Analytics', category: 'marketing', level: 5, color: '#10b981' },
      { id: 'google-ads', name: 'Google Ads/FB Ads', category: 'marketing', level: 5, color: '#10b981' },

      // Design Tools
      { id: 'photoshop', name: 'Adobe Photoshop', category: 'design', level: 5, color: '#f59e0b' },
      { id: 'illustrator', name: 'Adobe Illustrator', category: 'design', level: 4, color: '#f59e0b' },
      { id: 'figma', name: 'Figma', category: 'design', level: 4, color: '#f59e0b' },
      { id: 'indesign', name: 'Adobe InDesign', category: 'design', level: 4, color: '#f59e0b' },

      // Automation & Analytics
      { id: 'selenium', name: 'Selenium', category: 'automation', level: 5, color: '#ef4444' },
      { id: 'rpa-tools', name: 'RPA Tools', category: 'automation', level: 4, color: '#ef4444' },
      { id: 'amazon-s3', name: 'Amazon S3', category: 'automation', level: 4, color: '#ef4444' },
      { id: 'hadoop', name: 'Hadoop', category: 'automation', level: 3, color: '#ef4444' },

      // Management & Leadership
      { id: 'team-leadership', name: 'Team Leadership', category: 'management', level: 5, color: '#8b5cf6' },
      { id: 'process-mgmt', name: 'Process Management', category: 'management', level: 5, color: '#8b5cf6' },
      { id: 'project-mgmt', name: 'Project Management', category: 'management', level: 5, color: '#8b5cf6' },
      { id: 'digital-marketing', name: 'Digital Marketing', category: 'management', level: 5, color: '#8b5cf6' },
      { id: 'process-enhancement', name: 'Process Enhancement', category: 'management', level: 5, color: '#8b5cf6' }
    ];

    // Define connections between technologies
    this.links = [
      // JavaScript connections
      { source: 'javascript', target: 'html', strength: 5 },
      { source: 'javascript', target: 'photoshop', strength: 4 },
      { source: 'javascript', target: 'google-analytics', strength: 4 },
      { source: 'javascript', target: 'adobe-target', strength: 4 },
      { source: 'javascript', target: 'optimizely', strength: 4 },

      // Python connections
      { source: 'python', target: 'selenium', strength: 5 },
      { source: 'python', target: 'sql', strength: 4 },
      { source: 'python', target: 'rpa-tools', strength: 4 },
      { source: 'python', target: 'hadoop', strength: 3 },

      // Marketing platform connections
      { source: 'salesforce', target: 'html', strength: 4 },
      { source: 'responsys', target: 'html', strength: 5 },
      { source: 'adobe-campaign', target: 'html', strength: 3 },
      { source: 'experian', target: 'html', strength: 5 },
      { source: 'google-analytics', target: 'google-ads', strength: 5 },
      { source: 'adobe-target', target: 'optimizely', strength: 4 },

      // Design tool connections
      { source: 'photoshop', target: 'illustrator', strength: 4 },
      { source: 'photoshop', target: 'indesign', strength: 3 },
      { source: 'figma', target: 'html', strength: 4 },

      // Automation connections
      { source: 'selenium', target: 'html', strength: 5 },
      { source: 'rpa-tools', target: 'process-enhancement', strength: 5 },
      { source: 'amazon-s3', target: 'salesforce', strength: 3 },
      { source: 'hadoop', target: 'sql', strength: 4 },

      // Management connections
      { source: 'team-leadership', target: 'process-mgmt', strength: 5 },
      { source: 'process-mgmt', target: 'project-mgmt', strength: 5 },
      { source: 'project-mgmt', target: 'process-enhancement', strength: 4 },
      { source: 'digital-marketing', target: 'google-ads', strength: 5 },
      { source: 'digital-marketing', target: 'google-analytics', strength: 5 },
      { source: 'process-enhancement', target: 'rpa-tools', strength: 4 }
    ];
  }

  openModal() {
    this.modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Initialize the network after modal is visible
    setTimeout(() => {
      this.initNetwork();
    }, 100);
  }

  closeModal() {
    this.modal.style.display = 'none';
    document.body.style.overflow = 'auto';

    // Clean up the network
    if (this.svg) {
      this.svg.remove();
      this.svg = null;
    }
    if (this.simulation) {
      this.simulation.stop();
      this.simulation = null;
    }
  }

  initNetwork() {
    // Clear previous network
    this.networkContainer.innerHTML = '';

    // Set dimensions
    this.width = this.networkContainer.clientWidth;
    this.height = this.networkContainer.clientHeight;

    // Create SVG
    this.svg = d3.select(this.networkContainer)
      .append('svg')
      .attr('width', this.width)
      .attr('height', this.height);

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    this.svg.call(zoom);

    // Create container for zoomable content
    const container = this.svg.append('g');

    // Create force simulation
    this.simulation = d3.forceSimulation(this.nodes)
      .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 100 - d.strength * 10))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(this.width / 2, this.height / 2))
      .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 5));

    // Create links
    const link = container.append('g')
      .selectAll('line')
      .data(this.links)
      .enter().append('line')
      .attr('class', 'link')
      .attr('stroke-width', d => Math.sqrt(d.strength));

    // Create nodes
    const node = container.append('g')
      .selectAll('circle')
      .data(this.nodes)
      .enter().append('circle')
      .attr('class', 'node')
      .attr('r', d => this.getNodeRadius(d))
      .attr('fill', d => d.color)
      .call(this.drag(this.simulation))
      .on('mouseover', (event, d) => this.showTooltip(event, d))
      .on('mouseout', () => this.hideTooltip())
      .on('click', (event, d) => this.highlightConnections(d));

    // Create labels
    const label = container.append('g')
      .selectAll('text')
      .data(this.nodes)
      .enter().append('text')
      .attr('class', 'node-label')
      .text(d => d.name)
      .attr('dy', d => this.getNodeRadius(d) + 15);

    // Update positions on simulation tick
    this.simulation.on('tick', () => {
      link
        .attr('x1', d => d.source.x)
        .attr('y1', d => d.source.y)
        .attr('x2', d => d.target.x)
        .attr('y2', d => d.target.y);

      node
        .attr('cx', d => d.x)
        .attr('cy', d => d.y);

      label
        .attr('x', d => d.x)
        .attr('y', d => d.y);
    });

    // Store references for later use
    this.linkElements = link;
    this.nodeElements = node;
    this.labelElements = label;
  }

  getNodeRadius(d) {
    return 8 + (d.level * 4); // Radius based on skill level
  }

  drag(simulation) {
    function dragstarted(event, d) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event, d) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }

    return d3.drag()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended);
  }

  showTooltip(event, d) {
    const tooltip = d3.select('body').append('div')
      .attr('class', 'node-tooltip')
      .style('opacity', 0);

    const skillStars = '★'.repeat(d.level) + '☆'.repeat(5 - d.level);

    tooltip.html(`
      <strong>${d.name}</strong><br>
      <span style="color: ${d.color};">Category: ${d.category}</span><br>
      Skill Level: ${skillStars}<br>
      <small>Click to highlight connections</small>
    `)
      .style('left', (event.pageX + 10) + 'px')
      .style('top', (event.pageY - 10) + 'px')
      .transition()
      .duration(200)
      .style('opacity', 1);
  }

  hideTooltip() {
    d3.selectAll('.node-tooltip').remove();
  }

  highlightConnections(selectedNode) {
    if (!this.nodeElements || !this.linkElements) return;

    // Get connected nodes
    const connectedNodes = new Set();
    connectedNodes.add(selectedNode.id);

    this.links.forEach(link => {
      if (link.source.id === selectedNode.id) {
        connectedNodes.add(link.target.id);
      }
      if (link.target.id === selectedNode.id) {
        connectedNodes.add(link.source.id);
      }
    });

    // Highlight connected nodes
    this.nodeElements
      .classed('highlighted', d => connectedNodes.has(d.id))
      .style('opacity', d => connectedNodes.has(d.id) ? 1 : 0.3);

    // Highlight connected links
    this.linkElements
      .classed('highlighted', d =>
        (d.source.id === selectedNode.id || d.target.id === selectedNode.id))
      .style('opacity', d =>
        (d.source.id === selectedNode.id || d.target.id === selectedNode.id) ? 1 : 0.1);

    // Highlight labels
    this.labelElements
      .style('opacity', d => connectedNodes.has(d.id) ? 1 : 0.3)
      .style('font-weight', d => d.id === selectedNode.id ? 'bold' : 'normal');
  }

  resetView() {
    if (!this.nodeElements || !this.linkElements) return;

    // Reset all highlighting
    this.nodeElements
      .classed('highlighted', false)
      .style('opacity', 1);

    this.linkElements
      .classed('highlighted', false)
      .style('opacity', 1);

    this.labelElements
      .style('opacity', 1)
      .style('font-weight', 'normal');

    // Reset simulation
    if (this.simulation) {
      this.simulation.alpha(0.3).restart();
    }

    this.isHighlightMode = false;
    this.highlightBtn.textContent = '⚡ Highlight Connections';
  }

  toggleHighlightMode() {
    this.isHighlightMode = !this.isHighlightMode;

    if (this.isHighlightMode) {
      this.highlightBtn.textContent = '🔍 Exit Highlight Mode';
      // Show instruction
      this.showInstruction('Click on any technology to see its connections');
    } else {
      this.highlightBtn.textContent = '⚡ Highlight Connections';
      this.resetView();
    }
  }

  groupByCategory() {
    if (!this.simulation) return;

    // Define category positions
    const categoryPositions = {
      programming: { x: this.width * 0.2, y: this.height * 0.3 },
      marketing: { x: this.width * 0.8, y: this.height * 0.3 },
      design: { x: this.width * 0.2, y: this.height * 0.7 },
      automation: { x: this.width * 0.8, y: this.height * 0.7 },
      management: { x: this.width * 0.5, y: this.height * 0.5 }
    };

    // Apply category-based forces
    this.simulation
      .force('category', d3.forceX(d => categoryPositions[d.category].x).strength(0.3))
      .force('categoryY', d3.forceY(d => categoryPositions[d.category].y).strength(0.3))
      .alpha(0.3)
      .restart();
  }

  showInstruction(message) {
    const instruction = d3.select(this.networkContainer)
      .append('div')
      .style('position', 'absolute')
      .style('top', '20px')
      .style('left', '50%')
      .style('transform', 'translateX(-50%)')
      .style('background', 'rgba(0, 0, 0, 0.8)')
      .style('color', 'white')
      .style('padding', '10px 20px')
      .style('border-radius', '20px')
      .style('font-size', '14px')
      .style('z-index', '1000')
      .text(message);

    setTimeout(() => {
      instruction.remove();
    }, 3000);
  }
}

// Initialize Tech Stack Visualizer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new TechStackVisualizer();
});