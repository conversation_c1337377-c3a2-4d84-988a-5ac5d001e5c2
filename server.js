import express from 'express';
import fetch from 'node-fetch';
import cors from 'cors';

const app = express();
const PORT = 3001;
const OPENROUTER_API_KEY = '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';

app.use(cors());
app.use(express.json());

app.post('/api/gemini-match', async (req, res) => {
  const { jobAd, profile } = req.body;
  if (!jobAd || !profile) {
    return res.status(400).json({ error: 'Missing jobAd or profile' });
  }

  const prompt = `You are an expert technical recruiter. Given the following candidate profile and a job description, provide:\n- A match score (0-100%)\n- A short summary of strengths and fit\n- Any gaps or areas for improvement\n\nCandidate Profile: ${JSON.stringify(profile)}\nJob Description: ${jobAd} .Make the response easy to read for recruiters by using icons for every section depending on scores and results`;

  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000', // Optional, update as needed
        'X-Title': 'Vasilis Portfolio' // Optional
      },
      body: JSON.stringify({
        //model: 'deepseek/deepseek-r1-0528:free',
        "model": "tngtech/deepseek-r1t2-chimera:free",
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });
    const data = await response.json();
    const text = data?.choices?.[0]?.message?.content || 'No response from OpenRouter.';
    res.json({ result: text });
  } catch (err) {
    res.status(500).json({ error: 'OpenRouter API error', details: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Recruiter Match backend running on http://localhost:${PORT}`);
}); 