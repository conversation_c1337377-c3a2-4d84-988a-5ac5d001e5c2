/* Main stylesheet for Portfolio Website Generator */ 

/* CSS Variables for Light/Dark Mode and Color Palette */
:root {
  --color-primary: #2563eb;
  --color-secondary: #1e293b;
  --color-accent: #f97316;
  --color-bg: #f8fafc;
  --color-text: #1e293b;
  --color-card: #fff;
  --color-border: #e5e7eb;
  --color-link: #2563eb;
  --color-link-hover: #f97316;
  --font-heading: 'Montserrat', Arial, sans-serif;
  --font-body: 'Open Sans', Arial, sans-serif;
  --transition: 0.2s cubic-bezier(.4,0,.2,1);
}
[data-theme="dark"] {
  --color-bg: #1e293b;
  --color-text: #f8fafc;
  --color-card: #334155;
  --color-border: #475569;
  --color-link: #60a5fa;
  --color-link-hover: #f97316;
}

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}
*, *:before, *:after {
  box-sizing: inherit;
}
body {
  margin: 0;
  font-family: var(--font-body);
  background: var(--color-bg);
  color: var(--color-text);
  min-height: 100vh;
  transition: background var(--transition), color var(--transition);
}
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  margin-top: 0;
}
a {
  color: var(--color-link);
  text-decoration: none;
  transition: color var(--transition);
}
a:hover {
  color: var(--color-link-hover);
}
header#navbar {
  position: sticky;
  top: 0;
  background: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  z-index: 100;
}
header nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem 1.5rem;
}
header nav a {
  margin: 0 0.5rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}
#theme-toggle, #menu-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  margin-left: 1rem;
  cursor: pointer;
  color: var(--color-text);
  transition: color var(--transition);
}
#menu-toggle {
  display: none;
}
.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 3rem 1rem 1rem 1rem;
}
.hero-headshot {
  border-radius: 50%;
  border: 4px solid var(--color-primary);
  width: 160px;
  height: 160px;
  object-fit: cover;
  margin-bottom: 1.5rem;
  background: var(--color-border);
}
.hero-content h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}
.hero-content h2 {
  font-size: 1.25rem;
  font-weight: 400;
  color: var(--color-secondary);
  margin-bottom: 1rem;
}
[data-theme="dark"] .hero-content h2 {
  color: #cbd5e1;
}
.hero-content p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}
.hero-socials {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}
.hero-socials a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-card);
  border: 1px solid var(--color-border);
  transition: background var(--transition), border var(--transition);
}
.hero-socials a:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
}
.hero-socials .icon {
  width: 24px;
  height: 24px;
  fill: var(--color-secondary);
  transition: fill var(--transition);
}
.hero-socials a:hover .icon {
  fill: #fff;
}
.hero-resume {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  color: #fff;
  border-radius: 2rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
  transition: background var(--transition), color var(--transition);
}
.hero-resume:hover {
  background: var(--color-accent);
  color: #fff;
}
footer {
  text-align: center;
  padding: 2rem 1rem 1rem 1rem;
  color: var(--color-secondary);
  background: none;
  font-size: 0.95rem;
}
[data-theme="dark"] footer {
  color: #cbd5e1;
}
/* Responsive Breakpoints */
@media (max-width: 1024px) {
  header nav {
    max-width: 100%;
    padding: 0.5rem 1rem;
  }
  .hero-content {
    padding: 3rem 1rem 1.5rem 1rem;
  }
}
@media (max-width: 768px) {
  header nav a {
    display: none;
  }
  #menu-toggle {
    display: inline-block;
  }
  .hero-content {
    padding: 2rem 0.5rem 1rem 0.5rem;
  }
}

/* Fade-in animation for sections */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
section {
  opacity: 0;
  animation: fadeInUp 1s ease forwards;
  animation-delay: 0.2s;
  margin-bottom: 2.5rem;
}
section.visible {
  opacity: 1;
}

/* Section headings */
section h2 {
  font-size: 2rem;
  color: var(--color-primary);
  margin-bottom: 1.25rem;
  text-align: center;
}

/* Skills tags */
.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}
.tag {
  background: var(--color-primary);
  color: #fff;
  border-radius: 1rem;
  padding: 0.4em 1em;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(37,99,235,0.08);
  transition: background var(--transition), color var(--transition);
}
.tag:hover {
  background: var(--color-accent);
  color: #fff;
}

/* Project cards */
.projects-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.project-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1rem;
  background: var(--color-bg);
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  border: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}
.project-card img {
  border-radius: 0.5rem;
  background: var(--color-border);
  width: 120px;
  height: 80px;
  object-fit: cover;
}
.project-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}
.demo-link, .repo-link {
  color: var(--color-link);
  font-weight: 600;
  margin-right: 0.5rem;
  font-size: 1rem;
}
.demo-link:hover, .repo-link:hover {
  color: var(--color-link-hover);
}

/* Timeline */
.timeline-placeholder ul {
  list-style: disc inside;
  margin: 1rem 0 0 0;
  padding: 0;
  text-align: left;
  max-width: 900px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.timeline-placeholder li {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

/* Resume card */
.resume-card {
  background: var(--color-bg);
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  border: 1px solid var(--color-border);
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.resume-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}
.resume-card ul {
  margin: 0;
  padding-left: 1.2em;
}
.resume-card li {
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

/* Contact section */
.contact-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.contact-info {
  text-align: center;
  margin-bottom: 1.5rem;
  width: 100%;
}
.contact-info p {
  margin: 0.2em 0;
}
.contact-placeholder form {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
  margin-top: 1.5rem;
}
.contact-placeholder input, .contact-placeholder textarea {
  width: 100%;
  padding: 1em 1.2em;
  border-radius: 0.5em;
  border: 1px solid var(--color-border);
  font-size: 1.08rem;
  font-family: var(--font-body);
  background: var(--color-bg);
  color: var(--color-text);
  resize: vertical;
  box-sizing: border-box;
}
.contact-placeholder textarea {
  min-height: 90px;
}
.contact-placeholder button[type="submit"] {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 2em;
  padding: 0.95em 0;
  font-weight: 600;
  font-size: 1.08rem;
  cursor: pointer;
  transition: background var(--transition);
  width: 100%;
  margin-top: 0.5rem;
}
.contact-placeholder button[type="submit"]:hover {
  background: var(--color-accent);
}
.map-placeholder svg {
  display: block;
  margin: 0 auto;
}

/* Responsive tweaks */
@media (max-width: 600px) {
  .project-card, .resume-card {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }
  .project-card img {
    width: 100%;
    height: auto;
    margin-bottom: 0.5rem;
  }
}

/* Resume section styles */
.resume-section {
  max-width: 1100px;
  margin: 2.5rem auto;
  background: var(--color-card);
  border-radius: 1rem;
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  padding: 2.5rem 1.5rem 2rem 1.5rem;
  border: 1px solid var(--color-border);
}
.resume-summary-card {
  background: var(--color-bg);
  border-radius: 0.75rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  border: 1px solid var(--color-border);
  box-shadow: 0 2px 8px rgba(37,99,235,0.04);
  margin-top: 1rem;
}
.resume-summary-card h3 {
  margin: 1.5rem 0 0.5rem 0;
  font-size: 1.2rem;
  color: var(--color-primary);
}
.resume-role, .resume-education {
  margin-bottom: 1.25rem;
  font-size: 1.05rem;
}
.resume-role ul {
  margin: 0.5rem 0 0 1.2em;
  padding: 0;
}
.resume-role li {
  margin-bottom: 0.4rem;
  font-size: 1rem;
}
.resume-date {
  color: var(--color-secondary);
  font-size: 0.95em;
  margin-left: 0.5em;
}
.resume-download {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 0.7rem 1.5rem;
  background: var(--color-primary);
  color: #fff;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
  transition: background var(--transition), color var(--transition);
}
.resume-download:hover {
  background: var(--color-accent);
  color: #fff;
}

/* Project group headings */
.project-group {
  margin-bottom: 2.5rem;
  width: 100%;
  max-width: 1100px;
  margin-left: auto;
  margin-right: auto;
}
.project-group h3 {
  color: var(--color-secondary);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  margin-top: 2rem;
  text-align: left;
  border-left: 4px solid var(--color-primary);
  padding-left: 0.75rem;
}

/* Journey section tweaks */
#journey .resume-card {
  margin-bottom: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

#journey .resume-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(37,99,235,0.12);
}

/* Journey location styling */
.journey-location {
  color: var(--color-text-muted);
  font-size: 0.9rem;
  font-style: italic;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.journey-location::before {
  content: "📍";
  margin-right: 0.5rem;
}

/* Journey details styling */
.journey-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
}

.journey-details li {
  margin-bottom: 0.75rem;
  line-height: 1.6;
  position: relative;
  padding-left: 1.2rem;
}

.journey-details li::before {
  content: "▸";
  color: var(--color-primary);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.journey-details li strong {
  color: var(--color-primary);
  font-weight: 600;
}

/* Enhanced resume date styling */
.resume-date {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.85rem;
  font-weight: 500;
  margin-left: 0.5rem;
  white-space: nowrap;
}

/* Journey section title enhancement */
#journey h2 {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

#journey h2::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  border-radius: 2px;
}

/* Timeline section tweaks */
#timeline .timeline-placeholder {
  margin-bottom: 2.5rem;
}

/* Resume section tweaks */
#resume .resume-section {
  margin-bottom: 2.5rem;
}

/* Project filter bar */
.project-filter-bar {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  margin-bottom: 2rem;
}
.project-filter {
  background: var(--color-card);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 2em;
  padding: 0.5em 1.2em;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition), color var(--transition), border var(--transition);
}
.project-filter.active, .project-filter:hover {
  background: var(--color-primary);
  color: #fff;
}

/* Hide project groups by default for filtering (JS will show/hide) */
.project-group { display: none; }
.project-group.active { display: block; }

/* Journey toggle button */
.journey-toggle {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 1.5em;
  padding: 0.4em 1.2em;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.7em;
  cursor: pointer;
  transition: background var(--transition);
}
.journey-toggle[aria-expanded="true"] {
  background: var(--color-accent);
}

/* Timeline entry animation */
.timeline-entry {
  opacity: 0;
  transform: translateX(-40px);
  transition: opacity 0.7s cubic-bezier(.4,0,.2,1), transform 0.7s cubic-bezier(.4,0,.2,1);
}
.timeline-entry.visible {
  opacity: 1;
  transform: none;
}

/* Wider main content containers - centered */
.resume-section, .projects-placeholder, .timeline-placeholder, .journey-placeholder, .skills-placeholder, .contact-placeholder {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}



/* Consistent section padding */
section > div {
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Skills & Expertise section styles */
.skills-title {
  text-align: center;
  font-size: 2.5rem;
  font-family: var(--font-heading);
  font-weight: 700;
  margin-bottom: 2.5rem;
}
.skills-title .highlight {
  color: var(--color-primary);
}
.skills-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  margin-bottom: 2rem;
}
.skill-card {
  background: var(--color-card);
  border: 1.5px solid #e5e7eb;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  padding: 1rem 1rem 1rem 1rem;
  min-width: 230px;
  max-width: 260px;
  flex: 1 1 230px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: transform 0.25s cubic-bezier(.4,0,.2,1), box-shadow 0.25s cubic-bezier(.4,0,.2,1), z-index 0.25s;
  z-index: 1;
}
.skill-card:hover, .skill-card:focus-within {
  transform: scale(1.06) translateY(-6px);
  box-shadow: 0 8px 32px rgba(37,99,235,0.16);
  z-index: 10;
}
.skill-card-title {
  color: var(--color-primary);
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1.1rem;
}
.skill-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}
.skill-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.08rem;
  margin-bottom: 0.7rem;
  font-family: var(--font-body);
}
.stars {
  font-family: inherit;
  letter-spacing: 0.1em;
  font-size: 1.1em;
  color: #f97316;
  font-weight: 700;
}
.stars::after {
  content: '';
}
/* Gray for empty stars */
.stars {
  unicode-bidi: bidi-override;
  direction: ltr;
}
.stars {
  --star-color: #f97316;
  --star-empty: #d1d5db;
}
.stars {
  color: var(--star-color);
}
.stars:after {
  content: '';
}
.stars {
  /* Color only filled stars orange, empty stars gray */
}
.stars {
  /* Use a custom font or SVG for more advanced stars if desired */
}
/* Responsive for skills cards */
@media (max-width: 900px) {
  .skills-cards {
    gap: 1.2rem;
  }
  .skill-card {
    min-width: 180px;
    max-width: 100%;
    padding: 1.2rem 1rem 1rem 1rem;
  }
}
@media (max-width: 600px) {
  .skills-cards {
    flex-direction: column;
    align-items: center;
  }
  .skill-card {
    width: 100%;
    min-width: 0;
    padding: 1rem 0.5rem;
  }
}

/* Timeline section styles */
.timeline-section {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}
.timeline-title {
  text-align: center;
  font-size: 2.3rem;
  font-family: var(--font-heading);
  font-weight: 700;
  margin-bottom: 2.5rem;
}
.timeline-title .highlight {
  color: var(--color-primary);
}
.timeline {
  position: relative;
  margin: 0 auto;
  padding: 2rem 0 2rem 0;
  max-width: 1100px;
  min-height: 100px;
}
.timeline:before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  width: 4px;
  height: 100%;
  background: #e5e7eb;
  transform: translateX(-50%);
  z-index: 0;
}
.timeline-item {
  position: relative;
  width: 50%;
  padding-bottom: 2.5rem;
  min-height: 120px;
}
.timeline-item.left {
  left: 0;
  text-align: right;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.timeline-item.right {
  left: 50%;
  text-align: left;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.timeline-icon {
  position: absolute;
  top: 24px;
  right: -24px;
  left: auto;
  z-index: 2;
  margin: 0;
}
.timeline-item.right .timeline-icon {
  left: -24px;
  right: auto;
}
.timeline-card {
  position: relative;
  background: var(--color-card);
  border: 1.5px solid #e5e7eb;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  padding: 1.4rem 1.5rem 1.2rem 1.4rem;
  min-width: 480px;
  max-width: 480px;
  flex: 1 1 480px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 0;
  transition: box-shadow 0.25s cubic-bezier(.4,0,.2,1), transform 0.25s cubic-bezier(.4,0,.2,1);
}
.timeline-item.left .timeline-card {
  margin-right: 40px;
}
.timeline-item.right .timeline-card {
  margin-left: 40px;
}
.timeline-item.left .timeline-card:hover, .timeline-item.right .timeline-card:hover {
  box-shadow: 0 8px 32px rgba(37,99,235,0.14);
  transform: scale(1.025) translateY(-3px);
}
.timeline-meta {
  color: var(--color-primary);
  font-size: 0.98rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}
.timeline-title-card {
  font-size: 1.18rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
}
.timeline-institution {
  color: var(--color-link);
  font-size: 1.05rem;
  font-weight: 600;
  margin-bottom: 0.2rem;
}
.timeline-desc {
  color: var(--color-text);
  font-size: 1rem;
  margin-bottom: 0.2rem;
}
.timeline-gpa, .timeline-id, .timeline-hours {
  color: #f97316;
  font-size: 0.98rem;
  margin-bottom: 0.2rem;
}
.timeline-link {
  color: var(--color-link);
  font-weight: 600;
  margin-top: 0.5rem;
  text-decoration: none;
  font-size: 1rem;
}
.timeline-link:hover {
  color: var(--color-accent);
}
@media (max-width: 900px) {
  .timeline {
    max-width: 100%;
  }
  .timeline-card {
    min-width: 0;
    max-width: 100%;
    padding: 1.2rem 1rem 1rem 1rem;
  }
}
@media (max-width: 700px) {
  .timeline {
    padding: 1rem 0;
  }
  .timeline:before {
    left: 24px;
    width: 3px;
  }
  .timeline-item, .timeline-item.left, .timeline-item.right {
    width: 100%;
    left: 0 !important;
    text-align: left;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    min-height: 0;
  }
  .timeline-icon {
    position: static;
    margin: 0 0.7rem 0 0;
  }
  .timeline-card {
    margin: 0;
    min-width: 0;
    max-width: 100%;
    padding: 1rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .timeline-section {
    padding: 0 0.5rem;
  }
  .timeline {
    padding: 1rem 0;
  }
}

/* Skill tooltip styles */
.skill-tooltip {
  position: absolute;
  left: 50%;
  top: -2.2rem;
  transform: translateX(-50%) scale(0.95);
  background: var(--color-card);
  color: var(--color-text);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(30,41,59,0.12);
  padding: 0.6em 1.1em;
  font-size: 0.98rem;
  font-family: var(--font-body);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.18s cubic-bezier(.4,0,.2,1), transform 0.18s cubic-bezier(.4,0,.2,1);
  z-index: 20;
}
.skill-list li:hover .skill-tooltip,
.skill-list li:focus-within .skill-tooltip {
  opacity: 1;
  transform: translateX(-50%) scale(1);
  pointer-events: auto;
}

/* Project, journey, timeline card hover effect */
.project-card:hover, .resume-card:hover, .timeline-card:hover {
  box-shadow: 0 8px 32px rgba(37,99,235,0.14);
  transform: scale(1.025) translateY(-3px);
}

/* Tech Stack Visualizer Styles */
.tech-stack-visualizer-container {
  text-align: center;
  margin: 1.5rem 0 2.5rem 0;
}

.tech-stack-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 1.8rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.25);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
  min-width: 240px;
  max-width: 320px;
}

.tech-stack-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.35);
}

.tech-stack-btn:active {
  transform: translateY(0px);
}

.tech-stack-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
  transition: left 0.4s;
}

.tech-stack-btn:hover::before {
  left: 100%;
}

.btn-icon {
  font-size: 1.3rem;
  animation: pulse 2s infinite;
  flex-shrink: 0;
}

.btn-text {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
}

.btn-subtitle {
  font-size: 0.8rem;
  opacity: 0.85;
  font-weight: 400;
  line-height: 1.1;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Tech Stack Modal */
.tech-stack-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.tech-stack-modal-content {
  background: var(--color-bg);
  margin: 2% auto;
  padding: 0;
  border-radius: 20px;
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.tech-stack-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  text-align: center;
  position: relative;
}

.tech-stack-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 700;
}

.tech-stack-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.close-btn {
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tech-stack-controls {
  padding: 1rem 2rem;
  background: var(--color-bg-secondary);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  border-bottom: 1px solid var(--color-border);
}

.control-btn {
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-btn:hover {
  background: var(--color-accent);
  transform: translateY(-2px);
}

.tech-stack-network {
  width: 100%;
  height: calc(90vh - 200px);
  background: var(--color-bg);
  position: relative;
}

.tech-stack-legend {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: var(--color-bg-secondary);
  padding: 1rem;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* Network Node Styles */
.node {
  cursor: pointer;
  stroke: #fff;
  stroke-width: 2px;
  transition: all 0.3s ease;
}

.node:hover {
  stroke-width: 3px;
  filter: brightness(1.2);
}

.node.highlighted {
  stroke: #ffd700;
  stroke-width: 4px;
  filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
}

.link {
  stroke: #999;
  stroke-opacity: 0.3;
  stroke-width: 1px;
  transition: all 0.3s ease;
}

.link.highlighted {
  stroke: #ffd700;
  stroke-opacity: 0.8;
  stroke-width: 3px;
  filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.4));
}

.node-label {
  font-family: 'Open Sans', sans-serif;
  font-size: 11px;
  font-weight: 600;
  fill: var(--color-text);
  text-anchor: middle;
  pointer-events: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.node-tooltip {
  position: absolute;
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.85rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  z-index: 1001;
  max-width: 250px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tech-stack-modal-content {
    width: 98%;
    height: 95vh;
    margin: 1% auto;
  }

  .tech-stack-controls {
    padding: 0.75rem 1rem;
  }

  .control-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .tech-stack-legend {
    position: relative;
    bottom: auto;
    left: auto;
    margin: 1rem;
  }

  .tech-stack-btn {
    min-width: 200px;
    max-width: 280px;
    padding: 0.9rem 1.5rem;
    font-size: 0.9rem;
    flex-direction: column;
    gap: 0.4rem;
  }

  .btn-text {
    font-size: 0.9rem;
  }

  .btn-subtitle {
    font-size: 0.75rem;
  }
}

/* Recruiter Match section styles */
.recruiter-match-section {
  max-width: 1000px;
  margin: 3rem auto 2.5rem auto;
  background: var(--color-card);
  border-radius: 1.2rem;
  box-shadow: 0 2px 16px rgba(30,41,59,0.07);
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  border: 1.5px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.recruiter-match-title {
  font-size: 2.1rem;
  font-family: var(--font-heading);
  font-weight: 700;
  text-align: center;
  margin-bottom: 1.2rem;
}
.recruiter-match-intro {
  text-align: center;
  font-size: 1.08rem;
  color: var(--color-secondary);
  margin-bottom: 1.5rem;
  max-width: 500px;
}
#recruiter-match-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.1rem;
  margin-bottom: 1.5rem;
  max-width: 850px;
}
#job-ad-input {
  width: 100%;
  max-width: 860px;
  min-width: 220px;
  border-radius: 0.7em;
  border: 1.5px solid var(--color-border);
  padding: 1.1em 1.2em;
  font-size: 1.08rem;
  font-family: var(--font-body);
  background: var(--color-bg);
  color: var(--color-text);
  resize: vertical;
  box-sizing: border-box;
  margin-bottom: 0.5rem;
  transition: border 0.2s;
}
#job-ad-input:focus {
  outline: none;
  border: 1.5px solid var(--color-primary);
}
#analyze-btn {
  background: var(--color-primary);
  color: #fff;
  border: none;
  border-radius: 2em;
  padding: 0.85em 2.2em;
  font-weight: 600;
  font-size: 1.08rem;
  cursor: pointer;
  transition: background var(--transition);
  margin-top: 0.2rem;
  box-shadow: 0 2px 8px rgba(37,99,235,0.08);
}
#analyze-btn:hover {
  background: var(--color-accent);
}
.recruiter-match-result {
  width: 100%;
  max-width: 600px;
  min-height: 2.5rem;
  background: #f8fafc;
  border-radius: 0.7em;
  border: 1.5px solid #e5e7eb;
  padding: 1.2em 1.2em 1.2em 1.2em;
  font-size: 1.08rem;
  color: var(--color-secondary);
  margin-top: 0.5rem;
  box-shadow: 0 2px 8px rgba(30,41,59,0.04);
  white-space: pre-line;
  display: none;
  max-width: 850px;
}
.recruiter-match-result.active {
  display: block;
  animation: fadeInUp 0.7s cubic-bezier(.4,0,.2,1);
}
@media (max-width: 900px) {
  #job-ad-input {
    max-width: 100%;
  }
} 

.resume-summary-card {
  background-color: #f9f9f9; /* Light background for contrast */
  border-radius: 8px; /* Rounded corners */
  padding: 15px; /* Spacing inside the card */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow */

}

.resume-summary-card h3 {
  font-size: 1.5em; /* Larger font for the main heading */
  margin-bottom: 10px; /* Space below the heading */
}

.resume-summary-card h4 {
  font-size: 1.2em; /* Slightly larger font for subheadings */
  margin-top: 20px; /* Space above subheadings */
}

.resume-summary-card p {
  line-height: 1.5; /* Improved readability */
  margin-bottom: 10px; /* Space below paragraphs */
}

.resume-expertise {
  list-style-type: disc; /* Bullet points for the list */
  margin-left: 15px; /* Indentation for the list */
}


