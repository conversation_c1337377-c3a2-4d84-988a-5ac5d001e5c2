# Portfolio Website Generator

A modern, recruiter-focused portfolio website built with HTML5, CSS3, and vanilla JavaScript. No frameworks, minimal dependencies, optimized for performance and content discoverability.

## Status Checklist

- [x] Project folders created
- [x] Base files created
- [x] HTML skeleton with all sections
- [x] CSS base (variables, layout, breakpoints, light/dark)
- [x] JS base (navigation, toggles, interactivity)
- [ ] Assets (images, icons, resume)
- [ ] NPM build scripts (minify, favicon, etc.)
- [ ] SEO files (robots.txt, sitemap.xml, meta tags)
- [ ] README with instructions 