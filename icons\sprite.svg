<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol id="linkedin" viewBox="0 0 24 24">
    <title>LinkedIn</title>
    <path d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.27c-.97 0-1.75-.79-1.75-1.76s.78-1.76 1.75-1.76 1.75.79 1.75 1.76-.78 1.76-1.75 1.76zm15.5 11.27h-3v-5.6c0-1.34-.03-3.07-1.87-3.07-1.87 0-2.16 1.46-2.16 2.97v5.7h-3v-10h2.89v1.36h.04c.4-.75 1.38-1.54 2.84-1.54 3.04 0 3.6 2 3.6 4.59v5.59z"/>
  </symbol>
  <symbol id="github" viewBox="0 0 24 24">
    <title>GitHub</title>
    <!-- Placeholder path, replace with actual GitHub SVG -->
    <circle cx="12" cy="12" r="10" fill="#e5e7eb"/>
    <text x="12" y="16" text-anchor="middle" font-size="10" fill="#333">GH</text>
  </symbol>
  <symbol id="x" viewBox="0 0 24 24">
    <title>X (Twitter)</title>
    <!-- Placeholder path, replace with actual X/Twitter SVG -->
    <rect x="2" y="2" width="20" height="20" rx="5" fill="#e5e7eb"/>
    <text x="12" y="16" text-anchor="middle" font-size="10" fill="#333">X</text>
  </symbol>
</svg> 